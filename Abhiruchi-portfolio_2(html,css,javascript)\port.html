<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Your Name | Portfolio</title>
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Font Awesome Icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <!-- Custom CSS -->
  <link rel="stylesheet" href="port.css">
</head>
<body class="bg-white dark:bg-gray-900 transition-colors duration-300">
  <!-- Navbar -->
  <nav class="p-6 flex justify-between items-center">
    <div class="text-xl font-bold text-gray-800 dark:text-white">Your Name</div>
    <div class="space-x-6">
      <a href="#home" class="hover:text-blue-600 dark:hover:text-blue-400">Home</a>
      <a href="#about" class="hover:text-blue-600 dark:hover:text-blue-400">About</a>
      <a href="#projects" class="hover:text-blue-600 dark:hover:text-blue-400">Projects</a>
      <a href="#contact" class="hover:text-blue-600 dark:hover:text-blue-400">Contact</a>
      <button id="theme-toggle" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
        <i class="fas fa-moon dark:hidden"></i>
        <i class="fas fa-sun hidden dark:inline"></i>
      </button>
    </div>
  </nav>

  <!-- Hero Section -->
  <section id="home" class="h-screen flex flex-col justify-center items-center text-center p-4">
    <h1 class="text-5xl font-bold text-gray-800 dark:text-white mb-4">Hi, I'm Your Name</h1>
    <p class="text-xl text-gray-600 dark:text-gray-300 mb-8">Web Developer | Designer | Problem Solver</p>
    <a href="#projects" class="bg-blue-600 text-white px-6 py-3 rounded hover:bg-blue-700 transition">View My Work</a>
  </section>

  <!-- About Section -->
  <section id="about" class="py-16 px-4">
    <div class="max-w-4xl mx-auto flex flex-col md:flex-row items-center gap-8">
      <img src="assets/images/profile.jpg" alt="Your Name" class="rounded-lg shadow-lg w-64 h-64 object-cover">
      <div>
        <h2 class="text-3xl font-bold text-gray-800 dark:text-white mb-4">About Me</h2>
        <p class="text-gray-600 dark:text-gray-300 leading-relaxed">
          Briefly describe your skills, experience, and passion. Highlight your expertise in technologies like JavaScript, React, Node.js, etc.
        </p>
        <a href="resume.pdf" download class="mt-4 inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Download Resume</a>
      </div>
    </div>
  </section>

  <!-- Projects Section -->
  <section id="projects" class="py-16 px-4">
    <h2 class="text-3xl font-bold text-center text-gray-800 dark:text-white mb-12">My Projects</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
      <!-- Project Card -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden hover:scale-105 transition">
        <img src="assets/images/project1.jpg" alt="Project 1" class="w-full h-48 object-cover">
        <div class="p-6">
          <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2">Project Title</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">Short description of the project and technologies used.</p>
          <a href="#" class="text-blue-600 dark:text-blue-400 hover:underline">View Project</a>
        </div>
      </div>
      <!-- Repeat for other projects -->
    </div>
  </section>

  <!-- Contact Section -->
  <section id="contact" class="py-16 px-4 bg-gray-100 dark:bg-gray-800">
    <div class="max-w-4xl mx-auto text-center">
      <h2 class="text-3xl font-bold text-gray-800 dark:text-white mb-8">Let's Connect</h2>
      <form name="contact" method="POST" data-netlify="true" class="max-w-md mx-auto">
        <input type="hidden" name="form-name" value="contact">
        <div class="mb-4">
          <input type="text" name="name" placeholder="Your Name" required class="w-full p-3 rounded bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>
        <div class="mb-4">
          <input type="email" name="email" placeholder="Your Email" required class="w-full p-3 rounded bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>
        <div class="mb-4">
          <textarea name="message" placeholder="Your Message" rows="4" required class="w-full p-3 rounded bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
        </div>
        <button type="submit" class="bg-blue-600 text-white px-6 py-3 rounded hover:bg-blue-700 transition">Send Message</button>
      </form>
    </div>
  </section>

  <!-- Footer -->
  <footer class="py-8 text-center text-gray-600 dark:text-gray-300">
    <div class="flex justify-center space-x-4 mb-4">
      <a href="#" class="hover:text-blue-600"><i class="fab fa-github"></i></a>
      <a href="#" class="hover:text-blue-600"><i class="fab fa-linkedin"></i></a>
      <a href="#" class="hover:text-blue-600"><i class="fab fa-twitter"></i></a>
    </div>
    <p>&copy; 2024 Your Name. All rights reserved.</p>
  </footer>

  <script src="port.css"></script>
</body>
</html>