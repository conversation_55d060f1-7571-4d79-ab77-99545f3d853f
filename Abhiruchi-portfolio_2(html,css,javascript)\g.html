<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON><PERSON></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        :root {
            --primary-color: #2e7d32;
            --secondary-color: #4caf50;
            --accent-color: #ffca28;
            --text-color: #333;
            --bg-color: #f4f4f4;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Poppins', sans-serif;
        }

        body {
            background: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 280px;
            background: #fff;
            padding: 2rem;
            position: fixed;
            height: 100vh;
            box-shadow: 4px 0 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .profile-pic {
            text-align: center;
            margin-bottom: 2rem;
        }

        .profile-pic img {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid var(--secondary-color);
            transition: transform 0.3s ease;
        }

        .profile-pic img:hover {
            transform: scale(1.1);
        }

        .sidebar nav ul {
            list-style: none;
        }

        .sidebar nav ul li {
            margin: 1.5rem 0;
        }

        .sidebar nav ul li a {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            padding: 0.5rem;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .sidebar nav ul li a:hover {
            background: var(--secondary-color);
            color: #fff;
            transform: translateX(10px);
        }

        .sidebar nav ul li a i {
            margin-right: 0.8rem;
            width: 20px;
        }

        .content {
            margin-left: 280px;
            flex: 1;
            padding: 2rem;
            background: linear-gradient(135deg, var(--secondary-color), #2196f3);
        }

        .hero {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            color: #fff;
            padding: 0 2rem;
            animation: fadeIn 1s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }

        .hero h2 {
            font-size: 2rem;
            color: var(--accent-color);
            margin-bottom: 1.5rem;
            font-weight: 400;
        }

        .hero p {
            font-size: 1.2rem;
            max-width: 700px;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .social-icons {
            margin: 2rem 0;
        }

        .social-icons a {
            color: #fff;
            font-size: 1.8rem;
            margin: 0 1rem;
            transition: all 0.3s ease;
        }

        .social-icons a:hover {
            color: var(--accent-color);
            transform: translateY(-5px);
        }

        .buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            justify-content: center;
        }

        .btn {
            background: var(--primary-color);
            color: #fff;
            padding: 0.8rem 2rem;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .btn:hover {
            background: var(--accent-color);
            color: var(--primary-color);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .hamburger {
            display: none;
            font-size: 2rem;
            color: #fff;
            position: fixed;
            top: 1rem;
            left: 1rem;
            z-index: 1000;
            cursor: pointer;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .content {
                margin-left: 0;
            }

            .hamburger {
                display: block;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .hero h2 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="hamburger"><i class="fas fa-bars"></i></div>
    <div class="container">
        <aside class="sidebar">
            <div class="profile-pic">
                <img src="https://via.placeholder.com/120" alt="Abhishek Kandel">
            </div>
            <nav>
                <ul>
                    <li><a href="#about"><i class="fas fa-user"></i> About</a></li>
                    <li><a href="#experience"><i class="fas fa-briefcase"></i> Experience</a></li>
                    <li><a href="#projects"><i class="fas fa-project-diagram"></i> Projects</a></li>
                    <li><a href="#skills"><i class="fas fa-tools"></i> Skills</a></li>
                    <li><a href="#certification"><i class="fas fa-certificate"></i> Certification</a></li>
                    <li><a href="#education"><i class="fas fa-graduation-cap"></i> Education</a></li>
                    <li><a href="#contact"><i class="fas fa-envelope"></i> Contact</a></li>
                    <li><a href="#resume"><i class="fas fa-file"></i> Resume</a></li>
                </ul>
            </nav>
        </aside>
        <main class="content">
            <section class="hero">
                <h1>Hi, I'm Abhishek Kandel!</h1>
                <h2>Full-Stack Developer & Problem Solver</h2>
                <p>Self-driven programmer with a passion for creating innovative solutions to complex real-world challenges.</p>
                <div class="social-icons">
                    <a href="https://linkedin.com" target="_blank"><i class="fab fa-linkedin"></i></a>
                    <a href="https://github.com" target="_blank"><i class="fab fa-github"></i></a>
                    <a href="https://twitter.com" target="_blank"><i class="fab fa-twitter"></i></a>
                    <a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i></a>
                </div>
                <div class="buttons">
                    <a href="#about" class="btn">Learn More</a>
                    <a href="#contact" class="btn">Get in Touch</a>
                    <a href="#projects" class="btn">View Projects</a>
                </div>
            </section>
        </main>
    </div>

    <script>
        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Mobile menu toggle
        const hamburger = document.querySelector('.hamburger');
        const sidebar = document.querySelector('.sidebar');
        
        hamburger.addEventListener('click', () => {
            sidebar.classList.toggle('active');
        });

        // Close sidebar when clicking a nav link on mobile
        document.querySelectorAll('.sidebar nav a').forEach(link => {
            link.addEventListener('click', () => {
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('active');
                }
            });
        });

        // Type animation (optional enhancement)
        const titles = ["Full-Stack Developer", "Problem Solver", "Tech Enthusiast"];
        let titleIndex = 0;
        let charIndex = 0;
        const h2 = document.querySelector('.hero h2');
        
        function type() {
            if (charIndex < titles[titleIndex].length) {
                h2.textContent += titles[titleIndex].charAt(charIndex);
                charIndex++;
                setTimeout(type, 100);
            } else {
                setTimeout(erase, 2000);
            }
        }

        function erase() {
            if (charIndex > 0) {
                h2.textContent = titles[titleIndex].substring(0, charIndex - 1);
                charIndex--;
                setTimeout(erase, 50);
            } else {
                titleIndex = (titleIndex + 1) % titles.length;
                setTimeout(type, 500);
            }
        }

        // Start typing animation
        setTimeout(type, 1000);
    </script>
</body>
</html>