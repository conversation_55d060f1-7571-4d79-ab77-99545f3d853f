/**
 * Vintage Clock JavaScript
 * Enhanced functionality for the vintage clock
 */

// Clock state
const clockState = {
    alarmTime: null,
    alarmSet: false,
    alarmSounding: false,
    stopwatchRunning: false,
    stopwatchStartTime: null,
    stopwatchElapsedTime: 0,
    timerDuration: 0,
    timerEndTime: null,
    timerRunning: false,
    timerInterval: null,
    soundEnabled: true,
    theme: 'vintage', // vintage, modern, dark
    displayFormat: '12hour', // 12hour, 24hour
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
};

// DOM Elements
let hourHand, minuteHand, secondHand, pendulum;
let digitalClock, dateDisplay, alarmDisplay, stopwatchDisplay, timerDisplay;
let tickSound, chimeSound, alarmSound;

// Initialize clock
function initClock() {
    // Get DOM elements
    hourHand = document.querySelector('.hour-hand');
    minuteHand = document.querySelector('.minute-hand');
    secondHand = document.querySelector('.second-hand');
    pendulum = document.querySelector('.pendulum');
    digitalClock = document.querySelector('.digital-time');
    dateDisplay = document.querySelector('.date-display');
    alarmDisplay = document.querySelector('.alarm-display');
    stopwatchDisplay = document.querySelector('.stopwatch-display');
    timerDisplay = document.querySelector('.timer-display');

    // Create minute markers
    createMinuteMarkers();

    // Initialize sounds
    initSounds();

    // Set initial time
    setTime();

    // Start clock
    setInterval(setTime, 1000);

    // Set up event listeners
    setupEventListeners();

    // Update displays
    updateDateDisplay();
    updateAlarmDisplay();
    updateStopwatchDisplay();
    updateTimerDisplay();
}

// Generate minute markers
function createMinuteMarkers() {
    const minuteMarkersContainer = document.querySelector('.minute-markers');
    for (let i = 0; i < 60; i++) {
        if (i % 5 !== 0) { // Skip positions where hour markers are
            const marker = document.createElement('div');
            marker.className = 'marker minute-marker';
            marker.style.setProperty('--rotation', `${i * 6}deg`);
            minuteMarkersContainer.appendChild(marker);
        }
    }
}

// Initialize sound effects
function initSounds() {
    // Create audio elements
    tickSound = new Audio('https://assets.mixkit.co/active_storage/sfx/2325/2325-preview.mp3');
    tickSound.volume = 0.2;

    chimeSound = new Audio('https://assets.mixkit.co/active_storage/sfx/2326/2326-preview.mp3');
    chimeSound.volume = 0.5;

    alarmSound = new Audio('https://assets.mixkit.co/active_storage/sfx/2869/2869-preview.mp3');
    alarmSound.volume = 0.7;
    alarmSound.loop = true;
}

// Update clock hands and digital displays
function setTime() {
    const now = new Date();

    // Update analog clock
    updateAnalogClock(now);

    // Update digital displays
    updateDigitalDisplay(now);

    // Check for alarm
    checkAlarm(now);

    // Play tick sound
    playTickSound();

    // Check for hour chime
    checkHourChime(now);

    // Animate pendulum
    animatePendulum();
}

// Update analog clock hands
function updateAnalogClock(now) {
    const seconds = now.getSeconds();
    const secondsDegrees = ((seconds / 60) * 360) + 90; // +90 to offset from default position
    secondHand.style.transform = `rotate(${secondsDegrees}deg)`;

    const minutes = now.getMinutes();
    const minutesDegrees = ((minutes / 60) * 360) + ((seconds / 60) * 6) + 90;
    minuteHand.style.transform = `rotate(${minutesDegrees}deg)`;

    const hours = now.getHours() % 12;
    const hoursDegrees = ((hours / 12) * 360) + ((minutes / 60) * 30) + 90;
    hourHand.style.transform = `rotate(${hoursDegrees}deg)`;
}

// Update digital time display
function updateDigitalDisplay(now) {
    if (!digitalClock) return;

    let hours = now.getHours();
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const seconds = now.getSeconds().toString().padStart(2, '0');
    let ampm = '';

    if (clockState.displayFormat === '12hour') {
        ampm = hours >= 12 ? ' PM' : ' AM';
        hours = hours % 12;
        hours = hours ? hours : 12; // Convert 0 to 12
    }

    digitalClock.textContent = `${hours}:${minutes}:${seconds}${ampm}`;
}

// Update date display
function updateDateDisplay() {
    if (!dateDisplay) return;

    const now = new Date();
    const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
    dateDisplay.textContent = now.toLocaleDateString(undefined, options);
}

// Animate pendulum
function animatePendulum() {
    pendulum.classList.toggle('swing-right');
    pendulum.classList.toggle('swing-left');
}

// Play tick sound
function playTickSound() {
    if (clockState.soundEnabled) {
        tickSound.currentTime = 0;
        tickSound.play().catch(e => console.log('Error playing tick sound:', e));
    }
}

// Check for hour chime
function checkHourChime(now) {
    if (now.getMinutes() === 0 && now.getSeconds() === 0 && clockState.soundEnabled) {
        chimeSound.currentTime = 0;
        chimeSound.play().catch(e => console.log('Error playing chime sound:', e));
    }
}

// Check for alarm
function checkAlarm(now) {
    if (!clockState.alarmSet || !clockState.alarmTime) return;

    const currentTime = now.getHours() * 3600 + now.getMinutes() * 60 + now.getSeconds();
    const alarmTime = clockState.alarmTime.getHours() * 3600 + clockState.alarmTime.getMinutes() * 60 + clockState.alarmTime.getSeconds();

    if (currentTime === alarmTime && !clockState.alarmSounding) {
        triggerAlarm();
    }
}

// Trigger alarm
function triggerAlarm() {
    clockState.alarmSounding = true;

    if (clockState.soundEnabled) {
        alarmSound.play().catch(e => console.log('Error playing alarm sound:', e));
    }

    // Show alarm notification
    const alarmNotification = document.querySelector('.alarm-notification');
    if (alarmNotification) {
        alarmNotification.classList.add('active');
    }
}

// Stop alarm
function stopAlarm() {
    clockState.alarmSounding = false;
    alarmSound.pause();
    alarmSound.currentTime = 0;

    // Hide alarm notification
    const alarmNotification = document.querySelector('.alarm-notification');
    if (alarmNotification) {
        alarmNotification.classList.remove('active');
    }
}

// Set alarm
function setAlarm(hours, minutes) {
    const now = new Date();
    const alarmTime = new Date();

    alarmTime.setHours(hours);
    alarmTime.setMinutes(minutes);
    alarmTime.setSeconds(0);

    // If alarm time is in the past, set it for tomorrow
    if (alarmTime < now) {
        alarmTime.setDate(alarmTime.getDate() + 1);
    }

    clockState.alarmTime = alarmTime;
    clockState.alarmSet = true;

    updateAlarmDisplay();
}

// Update alarm display
function updateAlarmDisplay() {
    if (!alarmDisplay) return;

    if (clockState.alarmSet && clockState.alarmTime) {
        let hours = clockState.alarmTime.getHours();
        const minutes = clockState.alarmTime.getMinutes().toString().padStart(2, '0');
        let ampm = '';

        if (clockState.displayFormat === '12hour') {
            ampm = hours >= 12 ? ' PM' : ' AM';
            hours = hours % 12;
            hours = hours ? hours : 12;
        }

        alarmDisplay.textContent = `Alarm: ${hours}:${minutes}${ampm}`;
        alarmDisplay.classList.add('active');
    } else {
        alarmDisplay.textContent = 'No Alarm Set';
        alarmDisplay.classList.remove('active');
    }
}

// Toggle alarm
function toggleAlarm() {
    if (clockState.alarmSounding) {
        stopAlarm();
    }

    clockState.alarmSet = !clockState.alarmSet;
    updateAlarmDisplay();
}

// Start stopwatch
function startStopwatch() {
    if (clockState.stopwatchRunning) return;

    clockState.stopwatchRunning = true;
    clockState.stopwatchStartTime = Date.now() - clockState.stopwatchElapsedTime;

    // Update stopwatch display every 10ms for better accuracy
    clockState.stopwatchInterval = setInterval(updateStopwatchTime, 10);
}

// Update stopwatch time
function updateStopwatchTime() {
    if (!clockState.stopwatchRunning) return;

    clockState.stopwatchElapsedTime = Date.now() - clockState.stopwatchStartTime;
    updateStopwatchDisplay();
}

// Update stopwatch display
function updateStopwatchDisplay() {
    if (!stopwatchDisplay) return;

    const elapsedTime = clockState.stopwatchElapsedTime;
    const milliseconds = Math.floor((elapsedTime % 1000) / 10).toString().padStart(2, '0');
    const seconds = Math.floor((elapsedTime / 1000) % 60).toString().padStart(2, '0');
    const minutes = Math.floor((elapsedTime / (1000 * 60)) % 60).toString().padStart(2, '0');
    const hours = Math.floor(elapsedTime / (1000 * 60 * 60)).toString().padStart(2, '0');

    stopwatchDisplay.textContent = `${hours}:${minutes}:${seconds}.${milliseconds}`;
}

// Pause stopwatch
function pauseStopwatch() {
    if (!clockState.stopwatchRunning) return;

    clockState.stopwatchRunning = false;
    clearInterval(clockState.stopwatchInterval);
}

// Reset stopwatch
function resetStopwatch() {
    pauseStopwatch();
    clockState.stopwatchElapsedTime = 0;
    updateStopwatchDisplay();
}

// Start timer
function startTimer(hours, minutes, seconds) {
    if (clockState.timerRunning) return;

    const totalSeconds = (hours * 3600) + (minutes * 60) + seconds;
    if (totalSeconds <= 0) return;

    clockState.timerDuration = totalSeconds * 1000;
    clockState.timerEndTime = Date.now() + clockState.timerDuration;
    clockState.timerRunning = true;

    // Update timer display every second
    clockState.timerInterval = setInterval(updateTimerTime, 1000);

    updateTimerDisplay();
}

// Update timer time
function updateTimerTime() {
    if (!clockState.timerRunning) return;

    const remainingTime = clockState.timerEndTime - Date.now();

    if (remainingTime <= 0) {
        timerComplete();
        return;
    }

    updateTimerDisplay(remainingTime);
}

// Update timer display
function updateTimerDisplay(remainingTime) {
    if (!timerDisplay) return;

    if (!clockState.timerRunning) {
        timerDisplay.textContent = 'Timer: Not Set';
        return;
    }

    const timeLeft = remainingTime || (clockState.timerEndTime - Date.now());
    const seconds = Math.floor((timeLeft / 1000) % 60).toString().padStart(2, '0');
    const minutes = Math.floor((timeLeft / (1000 * 60)) % 60).toString().padStart(2, '0');
    const hours = Math.floor(timeLeft / (1000 * 60 * 60)).toString().padStart(2, '0');

    timerDisplay.textContent = `Timer: ${hours}:${minutes}:${seconds}`;
}

// Timer complete
function timerComplete() {
    clockState.timerRunning = false;
    clearInterval(clockState.timerInterval);

    if (clockState.soundEnabled) {
        alarmSound.play().catch(e => console.log('Error playing timer sound:', e));
        setTimeout(() => {
            alarmSound.pause();
            alarmSound.currentTime = 0;
        }, 3000);
    }

    timerDisplay.textContent = 'Timer Complete!';

    // Reset timer after 5 seconds
    setTimeout(() => {
        timerDisplay.textContent = 'Timer: Not Set';
    }, 5000);
}

// Cancel timer
function cancelTimer() {
    if (!clockState.timerRunning) return;

    clockState.timerRunning = false;
    clearInterval(clockState.timerInterval);
    updateTimerDisplay();
}

// Toggle sound
function toggleSound() {
    clockState.soundEnabled = !clockState.soundEnabled;

    const soundToggle = document.querySelector('.sound-toggle');
    if (soundToggle) {
        soundToggle.textContent = clockState.soundEnabled ? 'Sound: On' : 'Sound: Off';
    }
}

// Change theme
function changeTheme(theme) {
    const clockFace = document.querySelector('.clock-face');
    const container = document.querySelector('.container');

    // Remove all theme classes
    clockFace.classList.remove('theme-vintage', 'theme-modern', 'theme-dark');
    container.classList.remove('theme-vintage', 'theme-modern', 'theme-dark');

    // Add new theme class
    clockFace.classList.add(`theme-${theme}`);
    container.classList.add(`theme-${theme}`);

    clockState.theme = theme;
}

// Toggle time format (12/24 hour)
function toggleTimeFormat() {
    clockState.displayFormat = clockState.displayFormat === '12hour' ? '24hour' : '12hour';

    const formatToggle = document.querySelector('.format-toggle');
    if (formatToggle) {
        formatToggle.textContent = clockState.displayFormat === '12hour' ? '12-Hour' : '24-Hour';
    }
}

// Change timezone
function changeTimezone(timezone) {
    clockState.timezone = timezone;
    updateDateDisplay();
}

// Set up event listeners
function setupEventListeners() {
    // Tab controls
    const tabButtons = document.querySelectorAll('.tab-btn');
    if (tabButtons.length) {
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all tabs
                document.querySelectorAll('.tab-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.querySelectorAll('.tab-pane').forEach(pane => {
                    pane.classList.remove('active');
                });

                // Add active class to clicked tab and corresponding content
                this.classList.add('active');
                const tabId = this.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });
    }

    // Alarm form
    const alarmForm = document.querySelector('.alarm-form');
    if (alarmForm) {
        alarmForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const hours = parseInt(this.querySelector('.alarm-hours').value);
            const minutes = parseInt(this.querySelector('.alarm-minutes').value);
            setAlarm(hours, minutes);
        });
    }

    // Alarm toggle
    const alarmToggle = document.querySelector('.alarm-toggle');
    if (alarmToggle) {
        alarmToggle.addEventListener('click', toggleAlarm);
    }

    // Alarm stop button
    const alarmStop = document.querySelector('.alarm-stop');
    if (alarmStop) {
        alarmStop.addEventListener('click', stopAlarm);
    }

    // Stopwatch controls
    const stopwatchStart = document.querySelector('.stopwatch-start');
    const stopwatchPause = document.querySelector('.stopwatch-pause');
    const stopwatchReset = document.querySelector('.stopwatch-reset');

    if (stopwatchStart) stopwatchStart.addEventListener('click', startStopwatch);
    if (stopwatchPause) stopwatchPause.addEventListener('click', pauseStopwatch);
    if (stopwatchReset) stopwatchReset.addEventListener('click', resetStopwatch);

    // Timer controls
    const timerForm = document.querySelector('.timer-form');
    const timerCancel = document.querySelector('.timer-cancel');

    if (timerForm) {
        timerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const hours = parseInt(this.querySelector('.timer-hours').value) || 0;
            const minutes = parseInt(this.querySelector('.timer-minutes').value) || 0;
            const seconds = parseInt(this.querySelector('.timer-seconds').value) || 0;
            startTimer(hours, minutes, seconds);
        });
    }

    if (timerCancel) timerCancel.addEventListener('click', cancelTimer);

    // Sound toggle
    const soundToggle = document.querySelector('.sound-toggle');
    if (soundToggle) soundToggle.addEventListener('click', toggleSound);

    // Theme controls
    const themeSelectors = document.querySelectorAll('.theme-selector');
    if (themeSelectors.length) {
        themeSelectors.forEach(selector => {
            selector.addEventListener('click', function() {
                // Remove active class from all theme selectors
                themeSelectors.forEach(sel => sel.classList.remove('active'));
                // Add active class to clicked selector
                this.classList.add('active');
                // Change theme
                changeTheme(this.dataset.theme);
            });
        });
    }

    // Format toggle
    const formatToggle = document.querySelector('.format-toggle');
    if (formatToggle) formatToggle.addEventListener('click', toggleTimeFormat);

    // Timezone selector
    const timezoneSelector = document.querySelector('.timezone-selector');
    if (timezoneSelector) {
        timezoneSelector.addEventListener('change', function() {
            changeTimezone(this.value);
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initClock);
