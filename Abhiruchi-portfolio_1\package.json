{"name": "portfolio-nithin", "version": "0.1.0", "private": true, "dependencies": {"@emailjs/browser": "^4.1.0", "@react-three/drei": "^9.97.5", "@react-three/fiber": "^8.15.16", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "flowbite": "^2.2.1", "framer-motion": "^11.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.0.1", "react-router-dom": "^6.22.0", "react-scripts": "5.0.1", "react-toastify": "^10.0.4", "react-vertical-timeline-component": "^3.6.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.4.1"}}