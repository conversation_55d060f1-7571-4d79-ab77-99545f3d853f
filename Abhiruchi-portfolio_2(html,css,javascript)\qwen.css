/* Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 200px;
    height: 100vh;
    background-color: #fff;
    padding: 20px;
}

.sidebar ul {
    list-style: none;
}

.sidebar li {
    margin-bottom: 15px;
    font-size: 16px;
    color: #333;
}

.sidebar i {
    margin-right: 10px;
}

/* Main Content */
.main-content {
    width: calc(100% - 200px);
    padding: 20px;
    background-color: #2e7d32; /* Green color */
    color: #fff;
    min-height: 100vh;
    overflow-y: auto;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.profile-picture {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
}

.greeting h1 {
    font-size: 36px;
    margin: 0;
}

.greeting span {
    background-color: #fff;
    padding: 5px;
    color: #2e7d32;
}

.tagline {
    font-size: 20px;
    color: #ffeb3b; /* Yellow color */
    margin-top: 10px;
}

.description {
    font-size: 18px;
    line-height: 1.6;
    margin-top: 10px;
}

.email-login {
    background-color: #2196f3; /* Blue color */
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.email-login:hover {
    background-color: #1976d2;
}

.social-icons {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.social-icons a {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 1px solid #fff;
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    margin: 0 5px;
    transition: transform 0.3s ease;
}

.social-icons a:hover {
    transform: scale(1.1);
}

.fa-linkedin::before,
.fa-facebook-f::before,
.fa-instagram::before,
.fa-twitter::before,
.fa-whatsapp::before,
.fa-discord::before,
.fa-envelope::before,
.fa-slack::before {
    color: #fff;
}

.action-buttons {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.btn {
    background-color: transparent;
    border: 1px solid #fff;
    color: #fff;
    padding: 10px 20px;
    border-radius: 5px;
    margin: 0 10px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn:hover {
    background-color: #fff;
    color: #2e7d32;
}

/* Chatbot Widget */
.chatbot-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 999;
}

.hand-gesture {
    width: 50px;
    animation: wave 2s infinite;
}

.chatbot-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #2196f3;
    padding: 10px;
    border-radius: 50%;
    width: 60px;
    height: 60px;
}

.chatbot-icon img {
    width: 40px;
    height: 40px;
}

.chatbot-icon span {
    font-size: 12px;
    color: #fff;
    margin-top: 5px;
}

@keyframes wave {
    0%, 100% {
        transform: rotate(0deg);
    }
    50% {
        transform: rotate(10deg);
    }
}