/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Playfair Display', serif;
    background-color: #f5f5f5;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-image: url('https://www.transparenttextures.com/patterns/wood-pattern.png');
    background-color: #e8d9c0;
}

.container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    max-width: 800px;
    width: 100%;
}

/* Clock styles */
.clock {
    position: relative;
    width: 400px;
    height: 550px;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 30px;
}

.clock-face {
    position: relative;
    width: 350px;
    height: 350px;
    border-radius: 50%;
    background: radial-gradient(#e8d9c0, #d2b48c);
    box-shadow:
        inset 0 0 20px rgba(0, 0, 0, 0.3),
        0 0 0 15px #8b4513,
        0 0 0 16px #5e2c0b,
        0 10px 35px rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

/* Aged effect with texture overlay */
.clock-face::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://www.transparenttextures.com/patterns/paper-fibers.png');
    opacity: 0.3;
    border-radius: 50%;
    pointer-events: none;
}

/* Clock brand */
.clock-brand {
    position: absolute;
    top: 25%;
    width: 100%;
    text-align: center;
    font-size: 24px;
    color: #5e2c0b;
    font-weight: bold;
    letter-spacing: 2px;
    text-shadow: 1px 1px 1px rgba(255, 255, 255, 0.3);
}

/* Digital time display */
.digital-time {
    position: absolute;
    bottom: 35%;
    width: 100%;
    text-align: center;
    font-size: 16px;
    color: #5e2c0b;
    font-weight: bold;
}

.digital-time.large {
    position: static;
    font-size: 32px;
    margin: 10px 0;
}

/* Date display */
.date-display {
    text-align: center;
    font-size: 16px;
    color: #5e2c0b;
    margin: 10px 0;
    font-weight: bold;
}

/* Hour markers */
.marker {
    position: absolute;
    width: 100%;
    height: 100%;
    transform: rotate(var(--rotation));
}

/* Hour marker positions */
.hour-marker:nth-child(1) { --rotation: 0deg; }
.hour-marker:nth-child(2) { --rotation: 30deg; }
.hour-marker:nth-child(3) { --rotation: 60deg; }
.hour-marker:nth-child(4) { --rotation: 90deg; }
.hour-marker:nth-child(5) { --rotation: 120deg; }
.hour-marker:nth-child(6) { --rotation: 150deg; }
.hour-marker:nth-child(7) { --rotation: 180deg; }
.hour-marker:nth-child(8) { --rotation: 210deg; }
.hour-marker:nth-child(9) { --rotation: 240deg; }
.hour-marker:nth-child(10) { --rotation: 270deg; }
.hour-marker:nth-child(11) { --rotation: 300deg; }
.hour-marker:nth-child(12) { --rotation: 330deg; }

.hour-marker span {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 24px;
    color: #5e2c0b;
    font-weight: bold;
}

.minute-marker::before {
    content: '';
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 10px;
    background-color: #5e2c0b;
}

/* Clock hands */
.hand {
    position: absolute;
    bottom: 50%;
    left: 50%;
    transform-origin: bottom center;
    z-index: 10;
    transition: transform 0.5s cubic-bezier(0.4, 2.1, 0.3, 0.8);
}

.hour-hand {
    width: 8px;
    height: 100px;
    background-color: #333;
    border-radius: 4px;
    transform: translateX(-50%) rotate(0deg);
}

.minute-hand {
    width: 6px;
    height: 130px;
    background-color: #333;
    border-radius: 3px;
    transform: translateX(-50%) rotate(0deg);
}

.second-hand {
    width: 2px;
    height: 140px;
    background-color: #b22222;
    border-radius: 1px;
    transform: translateX(-50%) rotate(0deg);
    transition: transform 0.1s cubic-bezier(0.4, 2.1, 0.3, 0.8);
}

/* Center circle */
.center-circle {
    position: absolute;
    width: 20px;
    height: 20px;
    background-color: #5e2c0b;
    border-radius: 50%;
    z-index: 11;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

/* Pendulum */
.pendulum-container {
    position: relative;
    width: 50px;
    height: 180px;
    margin-top: 20px;
    overflow: hidden;
}

.pendulum {
    position: absolute;
    top: 0;
    left: 50%;
    transform-origin: top center;
    transform: translateX(-50%);
    animation: swing 2s infinite alternate ease-in-out;
}

.pendulum-rod {
    width: 4px;
    height: 140px;
    background-color: #5e2c0b;
    margin: 0 auto;
}

.pendulum-bob {
    width: 40px;
    height: 40px;
    background: radial-gradient(#d4af37, #b8860b);
    border-radius: 50%;
    margin: 0 auto;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}

/* Pendulum animation */
@keyframes swing {
    0% {
        transform: translateX(-50%) rotate(-10deg);
    }
    100% {
        transform: translateX(-50%) rotate(10deg);
    }
}

.swing-left {
    animation: swing-left 1s infinite alternate ease-in-out;
}

.swing-right {
    animation: swing-right 1s infinite alternate ease-in-out;
}

@keyframes swing-left {
    from {
        transform: translateX(-50%) rotate(10deg);
    }
    to {
        transform: translateX(-50%) rotate(-10deg);
    }
}

@keyframes swing-right {
    from {
        transform: translateX(-50%) rotate(-10deg);
    }
    to {
        transform: translateX(-50%) rotate(10deg);
    }
}

/* Alarm notification */
.alarm-notification {
    position: absolute;
    top: 20px;
    right: 20px;
    background-color: #b22222;
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    display: none;
    align-items: center;
    gap: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    z-index: 100;
}

.alarm-notification.active {
    display: flex;
    animation: pulse 1s infinite alternate;
}

.alarm-notification i {
    font-size: 20px;
}

.alarm-notification button {
    background-color: white;
    color: #b22222;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
    font-weight: bold;
}

@keyframes pulse {
    from { transform: scale(1); }
    to { transform: scale(1.05); }
}

/* Clock controls */
.clock-controls {
    background-color: #f5f5f5;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 600px;
    overflow: hidden;
    border: 2px solid #8b4513;
}

.control-tabs {
    display: flex;
    background-color: #8b4513;
}

.tab-btn {
    background-color: transparent;
    border: none;
    color: white;
    padding: 15px;
    flex: 1;
    cursor: pointer;
    font-family: 'Playfair Display', serif;
    font-size: 16px;
    transition: background-color 0.3s;
}

.tab-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.tab-btn.active {
    background-color: #5e2c0b;
    font-weight: bold;
}

.tab-content {
    padding: 20px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.control-group {
    margin-bottom: 20px;
}

.control-group h3 {
    color: #5e2c0b;
    margin-bottom: 10px;
    font-size: 20px;
    border-bottom: 1px solid #d2b48c;
    padding-bottom: 5px;
}

/* Form controls */
.time-inputs {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 15px;
}

input[type="number"] {
    width: 60px;
    padding: 8px;
    border: 1px solid #d2b48c;
    border-radius: 5px;
    font-family: 'Playfair Display', serif;
    text-align: center;
}

.btn {
    background-color: #8b4513;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-family: 'Playfair Display', serif;
    margin-right: 5px;
    margin-bottom: 5px;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #5e2c0b;
}

.btn.active {
    background-color: #5e2c0b;
    font-weight: bold;
}

/* Stopwatch and timer displays */
.stopwatch-display,
.timer-display,
.alarm-display {
    font-size: 24px;
    color: #5e2c0b;
    margin: 15px 0;
    font-family: monospace;
    font-weight: bold;
}

.stopwatch-controls,
.timer-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.alarm-display.active {
    color: #b22222;
}

/* Theme styles */
.theme-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

/* Theme: Vintage (default) */
.theme-vintage {
    /* Already styled in the base styles */
}

/* Theme: Modern */
.theme-modern .clock-face {
    background: radial-gradient(#f0f0f0, #d0d0d0);
    box-shadow:
        inset 0 0 20px rgba(0, 0, 0, 0.1),
        0 0 0 15px #303030,
        0 0 0 16px #202020,
        0 10px 35px rgba(0, 0, 0, 0.4);
}

.theme-modern .hour-marker span,
.theme-modern .clock-brand,
.theme-modern .digital-time {
    color: #303030;
}

.theme-modern .minute-marker::before {
    background-color: #303030;
}

.theme-modern .center-circle {
    background-color: #303030;
}

.theme-modern .pendulum-rod {
    background-color: #303030;
}

.theme-modern .pendulum-bob {
    background: radial-gradient(#f0f0f0, #a0a0a0);
}

/* Theme: Dark */
.theme-dark .clock-face {
    background: radial-gradient(#2c2c2c, #1a1a1a);
    box-shadow:
        inset 0 0 20px rgba(0, 0, 0, 0.5),
        0 0 0 15px #000000,
        0 0 0 16px #333333,
        0 10px 35px rgba(0, 0, 0, 0.8);
}

.theme-dark .hour-marker span,
.theme-dark .clock-brand,
.theme-dark .digital-time {
    color: #f0f0f0;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
}

.theme-dark .minute-marker::before {
    background-color: #f0f0f0;
}

.theme-dark .center-circle {
    background-color: #f0f0f0;
}

.theme-dark .hour-hand,
.theme-dark .minute-hand {
    background-color: #f0f0f0;
}

.theme-dark .second-hand {
    background-color: #ff6b6b;
}

.theme-dark .pendulum-rod {
    background-color: #f0f0f0;
}

.theme-dark .pendulum-bob {
    background: radial-gradient(#ff6b6b, #b22222);
}

/* Timezone selector */
.timezone-selector {
    width: 100%;
    padding: 10px;
    border: 1px solid #d2b48c;
    border-radius: 5px;
    font-family: 'Playfair Display', serif;
    background-color: white;
    color: #5e2c0b;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .clock {
        width: 350px;
        height: 500px;
    }

    .clock-face {
        width: 300px;
        height: 300px;
    }

    .hour-hand {
        height: 80px;
    }

    .minute-hand {
        height: 100px;
    }

    .second-hand {
        height: 110px;
    }

    .hour-marker span {
        font-size: 20px;
        top: 15px;
    }

    .pendulum-container {
        height: 150px;
    }

    .pendulum-rod {
        height: 110px;
    }

    .pendulum-bob {
        width: 30px;
        height: 30px;
    }

    .tab-btn {
        padding: 10px 5px;
        font-size: 14px;
    }
}

@media (max-width: 500px) {
    .clock {
        width: 280px;
        height: 400px;
    }

    .clock-face {
        width: 250px;
        height: 250px;
    }

    .hour-hand {
        height: 70px;
    }

    .minute-hand {
        height: 90px;
    }

    .second-hand {
        height: 100px;
    }

    .hour-marker span {
        font-size: 16px;
        top: 12px;
    }

    .pendulum-container {
        height: 120px;
    }

    .pendulum-rod {
        height: 90px;
    }

    .pendulum-bob {
        width: 25px;
        height: 25px;
    }

    .tab-btn {
        font-size: 12px;
        padding: 8px 5px;
    }

    .digital-time.large {
        font-size: 24px;
    }

    .stopwatch-display,
    .timer-display,
    .alarm-display {
        font-size: 20px;
    }
}
