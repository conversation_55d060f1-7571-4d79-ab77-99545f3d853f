// Add smooth scrolling to anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth'
            });
        }
    });
});

// Handle contact form submission
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form values
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const message = document.getElementById('message').value;

            // Simple validation
            if (!name || !email || !message) {
                alert('Please fill in all fields');
                return;
            }

            // Here you would typically send the data to a server
            // For now, we'll just show a success message
            alert(`Thank you, ${name}! Your message has been received. I'll get back to you soon.`);

            // Reset the form
            contactForm.reset();
        });
    }

    // Highlight active section in navigation
    const sections = document.querySelectorAll('section');
    const navLinks = document.querySelectorAll('.sidebar nav ul li a');

    function highlightNavigation() {
        let scrollPosition = window.scrollY;

        // Add some offset to account for the header
        scrollPosition += 100;

        // Find the current section
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            const sectionId = section.getAttribute('id');

            if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                // Remove active class from all links
                navLinks.forEach(link => {
                    link.classList.remove('active');
                });

                // Add active class to current section link
                const currentLink = document.querySelector(`.sidebar nav ul li a[href="#${sectionId}"]`);
                if (currentLink) {
                    currentLink.classList.add('active');
                }
            }
        });
    }

    // Initial call to highlight the current section
    highlightNavigation();

    // Highlight the current section on scroll
    window.addEventListener('scroll', highlightNavigation);
});

// Handle certificate image uploads and management
document.addEventListener('DOMContentLoaded', function() {
    // Certificate management functionality
    initCertificateUpload();

    // Load saved certificates from localStorage
    loadSavedCertificates();
});

/**
 * Initialize certificate upload functionality
 */
function initCertificateUpload() {
    // Get all certificate upload inputs
    const certificateInputs = document.querySelectorAll('.certificate-input');

    // Add event listeners to each input
    certificateInputs.forEach(input => {
        input.addEventListener('change', handleCertificateUpload);
    });

    // Make certificate titles editable
    document.querySelectorAll('.certificate-title').forEach(title => {
        title.addEventListener('blur', function() {
            if (!this.textContent.trim()) {
                const index = this.closest('.certificate-frame')
                    .querySelector('.certificate-input').dataset.index;
                this.textContent = `Certificate #${index}`;
            }

            // Save the updated title to localStorage
            const previewContainer = this.closest('.certificate-frame').querySelector('.preview-container');
            if (previewContainer.querySelector('img')) {
                saveCertificateData();
            }
        });
    });
}

/**
 * Handle certificate image upload
 * @param {Event} e - The change event
 */
function handleCertificateUpload(e) {
    const file = e.target.files[0];
    if (!file) return;

    const frameElement = this.closest('.certificate-frame');
    const previewContainer = frameElement.querySelector('.preview-container');
    const uploadLabel = frameElement.querySelector('.upload-label');
    const titleElement = frameElement.querySelector('.certificate-title');

    // Validate file is an image
    if (!file.type.match('image.*')) {
        alert('Please select an image file (jpg, png, etc.)');
        return;
    }

    // Create a FileReader to read the image
    const reader = new FileReader();

    reader.onload = function(event) {
        // Create an image element
        const img = document.createElement('img');
        img.src = event.target.result;
        img.alt = 'Certificate Preview';

        // Clear the preview container and add the image
        previewContainer.innerHTML = '';
        previewContainer.appendChild(img);
        previewContainer.style.display = 'block';

        // Hide the upload label
        uploadLabel.style.display = 'none';

        // Save to localStorage for persistence
        // Use setTimeout to ensure the DOM is updated before saving
        setTimeout(saveCertificateData, 100);
    };

    // Handle errors
    reader.onerror = function() {
        alert('Error reading the file. Please try again.');
    };

    // Read the image file
    reader.readAsDataURL(file);

    // Update the certificate title with the file name
    if (titleElement) {
        // Remove file extension and use as title
        const fileName = file.name.replace(/\.[^/.]+$/, "");
        titleElement.textContent = fileName;
    }

    // Save to localStorage for persistence (optional)
    saveCertificateData();
}

// Remove certificate function removed

/**
 * Save certificate data to localStorage for persistence
 */
function saveCertificateData() {
    const certificates = [];

    // Collect data from all certificate frames
    document.querySelectorAll('.certificate-frame').forEach((frame, index) => {
        const previewContainer = frame.querySelector('.preview-container');
        const titleElement = frame.querySelector('.certificate-title');
        const imgElement = previewContainer.querySelector('img');

        // Only save if there's an image
        if (imgElement && previewContainer.style.display !== 'none') {
            certificates.push({
                index: index + 1,
                title: titleElement.textContent,
                imageData: imgElement.src,
                display: true
            });
        }
    });

    // Save to localStorage
    if (certificates.length > 0) {
        localStorage.setItem('portfolioCertificates', JSON.stringify(certificates));
        console.log(`Saved ${certificates.length} certificates to localStorage`);
    }
}

/**
 * Load saved certificates from localStorage
 */
function loadSavedCertificates() {
    try {
        // Get saved certificate data
        const savedData = localStorage.getItem('portfolioCertificates');
        if (!savedData) return;

        const certificates = JSON.parse(savedData);
        console.log(`Loading ${certificates.length} saved certificates`);

        // Apply saved data to certificate frames
        certificates.forEach(cert => {
            if (cert.index > 6) return; // Skip if more than our 6 frames

            const frameElement = document.querySelector(`#cert-upload-${cert.index}`).closest('.certificate-frame');
            const previewContainer = frameElement.querySelector('.preview-container');
            const uploadLabel = frameElement.querySelector('.upload-label');
            const titleElement = frameElement.querySelector('.certificate-title');

            // Create and set the image
            const img = document.createElement('img');
            img.src = cert.imageData;
            img.alt = 'Certificate Preview';

            // Update the DOM
            previewContainer.innerHTML = '';
            previewContainer.appendChild(img);
            previewContainer.style.display = 'block';

            // Hide the upload label
            uploadLabel.style.display = 'none';

            // Set the title
            if (cert.title) {
                titleElement.textContent = cert.title;
            }
        });
    } catch (error) {
        console.error('Error loading saved certificates:', error);
    }
}

console.log("Website loaded successfully!");