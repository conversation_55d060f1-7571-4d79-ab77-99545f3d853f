<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Abhir<PERSON> Yeole - Portfolio</title>
    <link rel="stylesheet" href="grok.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <aside class="sidebar">

            <nav>
                <ul>
                    <li><a href="#about"><i class="fas fa-user"></i> About</a></li>
                    <li><a href="#projects"><i class="fas fa-project-diagram"></i> Projects</a></li>
                    <li><a href="#skills"><i class="fas fa-tools"></i> Skills</a></li>
                    <li><a href="#certification"><i class="fas fa-certificate"></i> Certification</a></li>
                    <li><a href="#education"><i class="fas fa-graduation-cap"></i> Education</a></li>
                    <li><a href="#contact"><i class="fas fa-envelope"></i> Contact</a></li>
                    <li><a href="#resume"><i class="fas fa-file"></i> Resume</a></li>
                </ul>
            </nav>
        </aside>
        <main class="content">
            <header>
                <a href="#" class="email-login">Email Portal Login</a>
            </header>
            <section class="hero">
                <h1>Hi, I'm Abhiruchi Yeole!</h1>
                <h2>A Passionate Developer</h2>
                <p>Self-driven, analytical, and passionate programmer with a curious mind who enjoys solving complex and challenging real-world problems.</p>
                <div class="social-icons">
                    <a href="#"><i class="fab fa-linkedin"></i></a>
                    <a href="#"><i class="fas fa-graduation-cap"></i></a>
                    <a href="#"><i class="fab fa-reddit"></i></a>
                    <a href="#"><i class="fab fa-bitbucket"></i></a>
                    <a href="#"><i class="fab fa-behance"></i></a>
                    <a href="#"><i class="fab fa-facebook"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-whatsapp"></i></a>
                    <a href="#"><i class="fas fa-bell"></i></a>
                    <a href="#"><i class="fas fa-envelope"></i></a>
                    <a href="#"><i class="fas fa-coffee"></i></a>
                </div>
                <div class="buttons">
                    <a href="#" class="btn">Read More</a>
                    <a href="#" class="btn">Contact Me</a>
                    <a href="#" class="btn">Read Blog</a>
                    <a href="#" class="btn">Book an Appointment</a>
                </div>
            </section>

            <section id="about" class="about-section">
                <h2>About</h2>
                <div class="about-content">
                    <p>I am a Computer Science and Engineering student at <strong><a href="https://ghrcem.raisoni.net/" target="_blank" rel="noopener">G.H. Raisoni College of Engineering and Management, Nagpur</a>.</strong> As a self-driven and passionate programmer, I bring a curious mind and a proactive approach to solving complex, real-world problems. Currently, I am engaged in projects in both Data Analytics and Web Development, which allow me to apply my technical skills to practical challenges. I have a solid foundation in coding and continuously strive to expand my knowledge and skill set through hands-on experience.</p>

                    <p>My educational journey began at <strong><a href="#" target="_blank" rel="noopener">Smt. Krishnabai Sarda English Medium School</a></strong>, followed by higher secondary studies at <strong><a href="#" target="_blank" rel="noopener">Sitaramji Ganorkar Science College, Pathrot</a></strong>. Over the years, I have developed a strong interest in computer science, and I'm always eager to explore new technologies and innovative solutions. I am also an avid reader and a natural problem-solver, and I love sharing the knowledge I've acquired through various sources and life experiences. My goal is to contribute meaningfully to the tech community while constantly growing as a developer and individual.</p>

                    <div class="skills-overview">
                        <div class="skill-category">
                            <h4>Languages:</h4>
                            <p>Python, Javascript, C,  C++, HTML/CSS</p>
                        </div>
                        <div class="skill-category">
                            <h4>Databases:</h4>
                            <p>MySQL, MongoDB</p>
                        </div>
                        <div class="skill-category">
                            <h4>Libraries:</h4>
                            <p>NumPy, Pandas, OpenCV</p>
                        </div>
                        <div class="skill-category">
                            <h4>Frameworks:</h4>
                            <p>Flask, Django, Node.js, Keras, TensorFlow, PyTorch, Bootstrap, Apache Beam</p>
                        </div>
                        <div class="skill-category">
                            <h4>Tools & Technologies:</h4>
                            <p>Git, Docker, AWS, JIRA</p>
                        </div>
                    </div>

                    <p>Looking for an opportunity to work in a challenging position combining my skills in Software Engineering, which provides professional development, interesting experiences and personal growth.</p>

                    <div class="current-focus">
                        <h4>Current Focus:</h4>
                        <p>Artificial intelligence, Web Development, Data Science</p>
                    </div>
                </div>
            </section>



            <section id="projects" class="projects-section">
                <h2>Projects</h2>

                <div class="projects-container">
                    <div class="project-card">
                        <div class="project-image">
                            <img src="https://via.placeholder.com/300x200" alt="Plant Disease Recognition System">
                        </div>
                        <div class="project-content">
                            <h3 class="project-title">Plant Disease Recognition System</h3>
                            <p class="project-description">Plant Disease Recognition System using Deep Learning</p>
                            <p class="project-status">Currently Doing</p>
                            <div class="project-tech">
                                <ul>
                                    <li><strong>Model Building:</strong> TensorFlow, CNN, Data Augmentation, tf datasets</li>
                                    <li><strong>Backend Server:</strong> tf Serving, FastAPI</li>
                                    <li><strong>Model Optimization:</strong> Quantization, TensorFlow Lite</li>
                                    <li><strong>Frontend & Deployment:</strong> React JS, React Native, Deployment GCP</li>
                                </ul>
                            </div>
                            <div class="project-links">
                                <a href="#" class="demo-link">Demo</a>
                                <a href="#" class="code-link">Code</a>
                            </div>
                        </div>
                    </div>

                    <div class="project-card">
                        <div class="project-image">
                            <img src="https://via.placeholder.com/300x200" alt="Birthday Greeting">
                        </div>
                        <div class="project-content">
                            <h3 class="project-title">Birthday Greeting</h3>
                            <p class="project-description">New way to wish birthday to your friend, if you are a coding geek.</p>
                            <p class="project-status">Completed</p>
                            <div class="project-tech">
                                <ul>
                                    <li><strong>Using:</strong> HTML, CSS, JavaScript</li>
                                </ul>
                            </div>
                            <div class="project-links">
                                <a href="#" class="demo-link">Demo</a>
                                <a href="#" class="code-link">Code</a>
                            </div>
                        </div>
                    </div>

                    <div class="project-card">
                        <div class="project-image">
                            <img src="https://via.placeholder.com/300x200" alt="Portfolio">
                        </div>
                        <div class="project-content">
                            <h3 class="project-title">Portfolio</h3>
                            <p class="project-description">Portfolio website for Ms. Priya Sharma</p>
                            <p class="project-status">Completed</p>
                            <div class="project-tech">
                                <ul>
                                    <li><strong>Using:</strong> HTML, CSS, JavaScript</li>
                                    <li><strong>Graphic Designing Tools:</strong> Adobe Photoshop, Adobe Illustrator</li>
                                </ul>
                            </div>
                            <div class="project-links">
                                <a href="#" class="demo-link">Demo</a>
                                <a href="#" class="code-link">Code</a>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section id="skills" class="skills-section">
                <h2>Skills</h2>
                <div class="skills-container">
                    <div class="skill-category-section">
                        <h3>Languages and Databases</h3>
                        <div class="skill-icons">
                            <div class="skill-icon">
                                <img src="images/python-logo.svg" alt="Python">
                                <span>Python</span>
                            </div>
                            <div class="skill-icon">
                                <img src="images/cpp-combined-logo.svg" alt="C/C++">
                                <span>C/C++</span>
                            </div>
                            <div class="skill-icon">
                                <img src="images/html5-logo.svg" alt="HTML5">
                                <span>HTML5</span>
                            </div>
                            <div class="skill-icon">
                                <img src="images/css3-logo.svg" alt="CSS3">
                                <span>CSS3</span>
                            </div>
                            <div class="skill-icon">
                                <img src="images/mysql-logo-new.svg" alt="MySQL">
                                <span>MySQL</span>
                            </div>
                            <div class="skill-icon">
                                <img src="images/javascript-logo.svg" alt="JavaScript">
                                <span>JavaScript</span>
                            </div>
                            <div class="skill-icon">
                                <img src="images/mongodb-logo.svg" alt="MongoDB">
                                <span>MongoDB</span>
                            </div>
                        </div>
                    </div>
                    <div class="skill-category-section">
                        <h3>Libraries</h3>
                        <div class="skill-icons">
                            <div class="skill-icon">
                                <img src="images/numpy-logo.svg" alt="NumPy">
                                <span>NumPy</span>
                            </div>
                            <div class="skill-icon">
                                <img src="images/pandas-logo.svg" alt="Pandas">
                                <span>Pandas</span>
                            </div>
                            <div class="skill-icon">
                                <img src="images/opencv-logo.svg" alt="OpenCV">
                                <span>OpenCV</span>
                            </div>
                            <div class="skill-icon">
                                <img src="images/scikit-learn-logo.svg" alt="scikit-learn">
                                <span>scikit-learn</span>
                            </div>
                            <div class="skill-icon">
                                <img src="images/matplotlib-logo.svg" alt="matplotlib">
                                <span>matplotlib</span>
                            </div>
                        </div>
                    </div>
                    <div class="skill-category-section">
                        <h3>Frameworks</h3>
                        <div class="skill-icons">
                            <div class="skill-icon">
                                <img src="images/django-logo.svg" alt="Django">
                                <span>Django</span>
                            </div>
                            <div class="skill-icon">
                                <img src="images/flask-logo.svg" alt="Flask">
                                <span>Flask</span>
                            </div>
                            <div class="skill-icon">
                                <img src="images/bootstrap-logo.svg" alt="Bootstrap">
                                <span>Bootstrap</span>
                            </div>
                            <div class="skill-icon">
                                <img src="images/keras-logo.svg" alt="Keras">
                                <span>Keras</span>
                            </div>
                            <div class="skill-icon">
                                <img src="images/tensorflow-logo.svg" alt="TensorFlow">
                                <span>TensorFlow</span>
                            </div>
                            <div class="skill-icon">
                                <img src="images/pytorch-logo.svg" alt="PyTorch">
                                <span>PyTorch</span>
                            </div>
                        </div>
                    </div>
                    <div class="skill-category-section">
                        <h3>Other</h3>
                        <div class="skill-icons">
                            <div class="skill-icon">
                                <img src="images/git-logo.svg" alt="Git">
                                <span>Git</span>
                            </div>
                            <div class="skill-icon">
                                <img src="images/aws-logo.svg" alt="AWS">
                                <span>AWS</span>
                            </div>
                            <div class="skill-icon">
                                <img src="images/heroku-logo.svg" alt="Heroku">
                                <span>Heroku</span>
                            </div>
                            <div class="skill-icon">
                                <img src="images/fastapi-logo.svg" alt="FastAPI">
                                <span>FastAPI</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section id="certification" class="certification-section">
                <h2>My Certifications</h2>
                <p>Here are some of my professional certifications and achievements:</p>

                <div class="certificates-container">
                    <!-- Certificate 1 -->
                    <div class="certificate-frame">
                        <div class="certificate-upload">
                            <input type="file" id="cert-upload-1" class="certificate-input" accept="image/*" data-index="1">
                            <label for="cert-upload-1" class="upload-label">
                                <i class="fas fa-certificate"></i>
                                <span>Upload Certificate</span>
                            </label>
                            <div class="preview-container">
                                <!-- Image preview will be inserted here -->
                            </div>

                        </div>
                        <div class="certificate-title" contenteditable="true">Certificate #1</div>
                    </div>

                    <!-- Certificate 2 -->
                    <div class="certificate-frame">
                        <div class="certificate-upload">
                            <input type="file" id="cert-upload-2" class="certificate-input" accept="image/*" data-index="2">
                            <label for="cert-upload-2" class="upload-label">
                                <i class="fas fa-certificate"></i>
                                <span>Upload Certificate</span>
                            </label>
                            <div class="preview-container">
                                <!-- Image preview will be inserted here -->
                            </div>

                        </div>
                        <div class="certificate-title" contenteditable="true">Certificate #2</div>
                    </div>

                    <!-- Certificate 3 -->
                    <div class="certificate-frame">
                        <div class="certificate-upload">
                            <input type="file" id="cert-upload-3" class="certificate-input" accept="image/*" data-index="3">
                            <label for="cert-upload-3" class="upload-label">
                                <i class="fas fa-certificate"></i>
                                <span>Upload Certificate</span>
                            </label>
                            <div class="preview-container">
                                <!-- Image preview will be inserted here -->
                            </div>

                        </div>
                        <div class="certificate-title" contenteditable="true">Certificate #3</div>
                    </div>

                    <!-- Certificate 4 -->
                    <div class="certificate-frame">
                        <div class="certificate-upload">
                            <input type="file" id="cert-upload-4" class="certificate-input" accept="image/*" data-index="4">
                            <label for="cert-upload-4" class="upload-label">
                                <i class="fas fa-certificate"></i>
                                <span>Upload Certificate</span>
                            </label>
                            <div class="preview-container">
                                <!-- Image preview will be inserted here -->
                            </div>

                        </div>
                        <div class="certificate-title" contenteditable="true">Certificate #4</div>
                    </div>

                    <!-- Certificate 5 -->
                    <div class="certificate-frame">
                        <div class="certificate-upload">
                            <input type="file" id="cert-upload-5" class="certificate-input" accept="image/*" data-index="5">
                            <label for="cert-upload-5" class="upload-label">
                                <i class="fas fa-certificate"></i>
                                <span>Upload Certificate</span>
                            </label>
                            <div class="preview-container">
                                <!-- Image preview will be inserted here -->
                            </div>

                        </div>
                        <div class="certificate-title" contenteditable="true">Certificate #5</div>
                    </div>

                    <!-- Certificate 6 -->
                    <div class="certificate-frame">
                        <div class="certificate-upload">
                            <input type="file" id="cert-upload-6" class="certificate-input" accept="image/*" data-index="6">
                            <label for="cert-upload-6" class="upload-label">
                                <i class="fas fa-certificate"></i>
                                <span>Upload Certificate</span>
                            </label>
                            <div class="preview-container">
                                <!-- Image preview will be inserted here -->
                            </div>

                        </div>
                        <div class="certificate-title" contenteditable="true">Certificate #6</div>
                    </div>
                </div>
            </section>

            <section id="education" class="education-section">
                <h2>EDUCATION</h2>

                <div class="education-container">
                    <div class="education-card">
                        <h3 class="university-name">G.H. RAISONI COLLEGE OF ENGINEERING AND MANAGEMENT </h3>
                        <p class="university-location">Nagpur, Maharashtra, India </p>
                        <div class="education-details">
                            <div class="degree-info">
                                <span class="label">Degree:</span>
                                <span class="value">B.Tech in Computer Science Engineering</span>
                            </div>
                            <div class="cgpa-info">
                                <span class="label">CGPA:</span>
                                <span class="value">9.58/10</span>
                            </div>
                        </div>

                        <div class="coursework">
                            <h4>Relevant Courseworks:</h4>
                            <ul>
                                <li>Data Structures & Algorithms</li>
                                <li>Database Management Systems</li>
                                <li>Web Development</li>
                                <li>Data Analytics</li>
                            </ul>
                        </div>
                    </div>

                    <div class="education-card">
                        <h3 class="university-name">SITARAMJI GANORKAR SCIENCE COLLEGE - (2022)</h3>
                        <p class="university-location">Pathrot, Maharashtra, India</p>
                        <div class="education-details">
                            <div class="degree-info">
                                <span class="label">Degree:</span>
                                <span class="value">Higher Secondary Education</span>
                            </div>
                            <div class="cgpa-info">
                                <span class="label">Percentage:</span>
                                <span class="value">91.82%</span>
                            </div>
                        </div>

                        <div class="coursework">

                            <p class="previous-school">
                                <strong>SMT. KRISHNABAI SARDA ENGLISH MEDIUM SCHOOL -(2020)</strong><br>
                                Anjangaon surji, Maharashtra, India<br><br>
                                <div class="school-detail">
                                    <span class="label">Degree:</span>
                                    <span class="value">Secondary School Education</span>
                                </div>
                                <div class="school-detail">
                                    <span class="label">Percentage:</span>
                                    <span class="value">92.80%</span>
                                </div>
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            <section id="contact" class="contact-section">
                <h2>Contact</h2>

                <div class="contact-container">
                    <div class="contact-form">
                        <h3>Contact Form</h3>
                        <form id="contactForm">
                            <div class="form-group">
                                <input type="text" id="name" name="name" placeholder="Name" required>
                            </div>
                            <div class="form-group">
                                <input type="email" id="email" name="email" placeholder="Email" required>
                            </div>
                            <div class="form-group">
                                <textarea id="message" name="message" placeholder="Message" required></textarea>
                            </div>
                            <button type="submit" class="submit-btn">Submit</button>
                        </form>
                    </div>
                </div>
            </section>

            <section id="resume" class="resume-section">
                <h2>Resume</h2>
                <div class="resume-container">
                    <div class="resume-content">
                        <p>Download my complete resume to learn more about my skills, experience, and qualifications.</p>
                        <a href="files/ABHIRUCHI%20YEOLE.pdf" class="download-btn" download><i class="fas fa-download"></i> Download Resume</a>
                    </div>
                </div>
            </section>

            <footer class="main-footer">
                <div class="footer-content">
                    <p class="copyright">Terms of Service & Privacy Policy © Copyright . All Rights Reserved. Designed by <a href="#">Abhiruchi Yeole</a></p>
                    <div class="footer-social">
                        <a href="#" title="LinkedIn"><i class="fab fa-linkedin"></i></a>
                        <a href="#" title="Academia"><i class="fas fa-graduation-cap"></i></a>
                        <a href="#" title="GitHub"><i class="fab fa-github"></i></a>
                        <a href="#" title="Behance"><i class="fab fa-behance"></i></a>
                        <a href="#" title="Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="#" title="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" title="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" title="WhatsApp"><i class="fab fa-whatsapp"></i></a>
                        <a href="#" title="Snapchat"><i class="fab fa-snapchat"></i></a>
                        <a href="#" title="Email"><i class="fas fa-envelope"></i></a>
                        <a href="#" title="Buy Me a Coffee"><i class="fas fa-coffee"></i></a>
                    </div>
                </div>
            </footer>
        </main>
    </div>
    <script src="grok.js"></script>
</body>
</html>