<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vintage Clock</title>
    <link rel="stylesheet" href="orc.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container theme-vintage">
        <div class="clock">
            <div class="clock-face theme-vintage">
                <!-- Hour markers -->
                <div class="marker hour-marker"><span>12</span></div>
                <div class="marker hour-marker"><span>1</span></div>
                <div class="marker hour-marker"><span>2</span></div>
                <div class="marker hour-marker"><span>3</span></div>
                <div class="marker hour-marker"><span>4</span></div>
                <div class="marker hour-marker"><span>5</span></div>
                <div class="marker hour-marker"><span>6</span></div>
                <div class="marker hour-marker"><span>7</span></div>
                <div class="marker hour-marker"><span>8</span></div>
                <div class="marker hour-marker"><span>9</span></div>
                <div class="marker hour-marker"><span>10</span></div>
                <div class="marker hour-marker"><span>11</span></div>

                <!-- Minute markers -->
                <div class="minute-markers"></div>

                <!-- Clock center and hands -->
                <div class="center-circle"></div>
                <div class="hand hour-hand"></div>
                <div class="hand minute-hand"></div>
                <div class="hand second-hand"></div>

                <!-- Clock brand -->
                <div class="clock-brand">Vintage</div>

                <!-- Digital time display -->
                <div class="digital-time"></div>
            </div>

            <!-- Clock pendulum -->
            <div class="pendulum-container">
                <div class="pendulum">
                    <div class="pendulum-rod"></div>
                    <div class="pendulum-bob"></div>
                </div>
            </div>

            <!-- Date display -->
            <div class="date-display"></div>

            <!-- Alarm notification -->
            <div class="alarm-notification">
                <i class="fas fa-bell"></i> Alarm!
                <button type="button" class="alarm-stop">Stop</button>
            </div>
        </div>

        <!-- Clock controls -->
        <div class="clock-controls">
            <!-- Tabs for different features -->
            <div class="control-tabs">
                <button type="button" class="tab-btn active" data-tab="general">General</button>
                <button type="button" class="tab-btn" data-tab="alarm">Alarm</button>
                <button type="button" class="tab-btn" data-tab="stopwatch">Stopwatch</button>
                <button type="button" class="tab-btn" data-tab="timer">Timer</button>
                <button type="button" class="tab-btn" data-tab="settings">Settings</button>
            </div>

            <!-- Tab content -->
            <div class="tab-content">
                <!-- General tab -->
                <div class="tab-pane active" id="general">
                    <div class="control-group">
                        <h3>Current Time</h3>
                        <div class="digital-time large"></div>
                        <div class="date-display"></div>
                    </div>
                </div>

                <!-- Alarm tab -->
                <div class="tab-pane" id="alarm">
                    <div class="control-group">
                        <h3>Set Alarm</h3>
                        <form class="alarm-form">
                            <div class="time-inputs">
                                <input type="number" class="alarm-hours" min="0" max="23" placeholder="HH" required>
                                <span>:</span>
                                <input type="number" class="alarm-minutes" min="0" max="59" placeholder="MM" required>
                            </div>
                            <button type="submit" class="btn">Set Alarm</button>
                        </form>
                        <div class="alarm-display">No Alarm Set</div>
                        <button type="button" class="btn alarm-toggle">Toggle Alarm</button>
                    </div>
                </div>

                <!-- Stopwatch tab -->
                <div class="tab-pane" id="stopwatch">
                    <div class="control-group">
                        <h3>Stopwatch</h3>
                        <div class="stopwatch-display">00:00:00.00</div>
                        <div class="stopwatch-controls">
                            <button type="button" class="btn stopwatch-start">Start</button>
                            <button type="button" class="btn stopwatch-pause">Pause</button>
                            <button type="button" class="btn stopwatch-reset">Reset</button>
                        </div>
                    </div>
                </div>

                <!-- Timer tab -->
                <div class="tab-pane" id="timer">
                    <div class="control-group">
                        <h3>Timer</h3>
                        <form class="timer-form">
                            <div class="time-inputs">
                                <input type="number" class="timer-hours" min="0" max="23" placeholder="HH">
                                <span>:</span>
                                <input type="number" class="timer-minutes" min="0" max="59" placeholder="MM">
                                <span>:</span>
                                <input type="number" class="timer-seconds" min="0" max="59" placeholder="SS">
                            </div>
                            <button type="submit" class="btn">Start Timer</button>
                        </form>
                        <div class="timer-display">Timer: Not Set</div>
                        <button type="button" class="btn timer-cancel">Cancel Timer</button>
                    </div>
                </div>

                <!-- Settings tab -->
                <div class="tab-pane" id="settings">
                    <div class="control-group">
                        <h3>Theme</h3>
                        <div class="theme-controls">
                            <button type="button" class="btn theme-selector active" data-theme="vintage">Vintage</button>
                            <button type="button" class="btn theme-selector" data-theme="modern">Modern</button>
                            <button type="button" class="btn theme-selector" data-theme="dark">Dark</button>
                        </div>
                    </div>

                    <div class="control-group">
                        <h3>Sound</h3>
                        <button type="button" class="btn sound-toggle">Sound: On</button>
                    </div>

                    <div class="control-group">
                        <h3>Time Format</h3>
                        <button type="button" class="btn format-toggle">12-Hour</button>
                    </div>

                    <div class="control-group">
                        <h3>Time Zone</h3>
                        <select class="timezone-selector" aria-label="Select timezone">
                            <option value="America/New_York">New York</option>
                            <option value="America/Chicago">Chicago</option>
                            <option value="America/Denver">Denver</option>
                            <option value="America/Los_Angeles">Los Angeles</option>
                            <option value="Europe/London">London</option>
                            <option value="Europe/Paris">Paris</option>
                            <option value="Asia/Tokyo">Tokyo</option>
                            <option value="Australia/Sydney">Sydney</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="orc.js"></script>
</body>
</html>
