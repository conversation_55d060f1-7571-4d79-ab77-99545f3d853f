<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title><PERSON><PERSON><PERSON><PERSON><PERSON>lio</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    body {
      background-color: #f0f2f5;
      color: #333;
    }

    .container {
      display: flex;
      min-height: 100vh;
    }

    .sidebar {
      width: 250px;
      background-color: #1b1f23;
      color: #fff;
      padding: 30px 20px;
      position: fixed;
      height: 100%;
      box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
    }

    .profile-pic img {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      object-fit: cover;
      margin-bottom: 20px;
    }

    .sidebar nav ul {
      list-style: none;
    }

    .sidebar nav ul li {
      margin: 20px 0;
    }

    .sidebar nav ul li a {
      color: #ccc;
      text-decoration: none;
      font-size: 16px;
      display: flex;
      align-items: center;
      transition: color 0.3s;
    }

    .sidebar nav ul li a:hover {
      color: #4caf50;
    }

    .sidebar nav ul li a i {
      margin-right: 10px;
    }

    .content {
      margin-left: 250px;
      flex: 1;
      padding: 40px 30px;
    }

    header {
      text-align: right;
      margin-bottom: 30px;
    }

    .email-login {
      background-color: #4caf50;
      color: #fff;
      padding: 10px 20px;
      text-decoration: none;
      border-radius: 30px;
      transition: background-color 0.3s;
    }

    .email-login:hover {
      background-color: #388e3c;
    }

    .hero {
      text-align: center;
      padding: 60px 20px;
      background: linear-gradient(to right, #43cea2, #185a9d);
      color: white;
      border-radius: 20px;
    }

    .hero h1 {
      font-size: 48px;
      margin-bottom: 10px;
    }

    .hero h2 {
      font-size: 26px;
      color: #ffeb3b;
      margin-bottom: 20px;
    }

    .hero p {
      font-size: 18px;
      max-width: 700px;
      margin: 0 auto 30px;
    }

    .social-icons a {
      color: white;
      font-size: 22px;
      margin: 0 10px;
      transition: color 0.3s;
    }

    .social-icons a:hover {
      color: #ffeb3b;
    }

    .buttons {
      margin-top: 30px;
      display: flex;
      justify-content: center;
      gap: 15px;
      flex-wrap: wrap;
    }

    .btn {
      background-color: #1b5e20;
      color: #fff;
      padding: 10px 25px;
      text-decoration: none;
      border-radius: 30px;
      transition: background-color 0.3s;
    }

    .btn:hover {
      background-color: #2e7d32;
    }

    footer {
      margin-top: 50px;
      text-align: center;
      color: #555;
    }

    section {
      padding: 60px 0;
      border-bottom: 1px solid #ddd;
    }

    @media (max-width: 768px) {
      .container {
        flex-direction: column;
      }
      .sidebar {
        width: 100%;
        position: static;
        text-align: center;
        padding: 20px 0;
      }
      .content {
        margin-left: 0;
        padding: 20px;
      }
    }
    .certificates-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
  margin-top: 30px;
}

.certificate-card {
  width: 280px;
  height: 200px;
  perspective: 1000px;
}

.certificate-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border: 4px solid #fff;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transition: transform 0.6s ease;
  transform-style: preserve-3d;
  border-radius: 12px;
}

.certificate-card:hover img {
  transform: rotateY(10deg) rotateX(5deg) scale(1.03);
}

  </style>
</head>
<body>
  <div class="container">
    <aside class="sidebar">
      <div class="profile-pic">
        <img src="https://via.placeholder.com/100" alt="Profile Picture" />
      </div>
      <nav>
        <ul>
          <li><a href="#about"><i class="fas fa-user"></i> About</a></li>
          <li><a href="#experience"><i class="fas fa-briefcase"></i> Experience</a></li>
          <li><a href="#projects"><i class="fas fa-project-diagram"></i> Projects</a></li>
          <li><a href="#skills"><i class="fas fa-tools"></i> Skills</a></li>
          <li><a href="#certification"><i class="fas fa-certificate"></i> Certification</a></li>
          <li><a href="#education"><i class="fas fa-graduation-cap"></i> Education</a></li>
          <li><a href="#contact"><i class="fas fa-envelope"></i> Contact</a></li>
          <li><a href="#resume"><i class="fas fa-file"></i> Resume</a></li>
        </ul>
      </nav>
    </aside>
    <main class="content">
      <header>
        <a href="#" class="email-login">Email Portal Login</a>
      </header>
      <section class="hero">
        <h1>Hi, I'm Abhishek Kandel!</h1>
        <h2>A Fast Learner</h2>
        <p>Self-driven, quick starter, passionate programmer with a curious mind who enjoys solving complex and challenging real-world problems.</p>
        <div class="social-icons">
          <a href="#"><i class="fab fa-linkedin"></i></a>
          <a href="#"><i class="fab fa-github"></i></a>
          <a href="#"><i class="fab fa-reddit"></i></a>
          <a href="#"><i class="fab fa-bitbucket"></i></a>
          <a href="#"><i class="fab fa-behance"></i></a>
          <a href="#"><i class="fab fa-facebook"></i></a>
          <a href="#"><i class="fab fa-instagram"></i></a>
          <a href="#"><i class="fab fa-twitter"></i></a>
          <a href="#"><i class="fab fa-whatsapp"></i></a>
        </div>
        <div class="buttons">
          <a href="#about" class="btn">Read More</a>
          <a href="#contact" class="btn">Contact Me</a>
          <a href="#blog" class="btn">Read Blog</a>
          <a href="#appointment" class="btn">Book an Appointment</a>
        </div>
      </section>

      <section id="about"><h2>About</h2><p>Details about Abhishek...</p></section>
      <section id="experience"><h2>Experience</h2><p>Experience details...</p></section>
      <section id="projects"><h2>Projects</h2><p>Project descriptions...</p></section>
      <section id="skills"><h2>Skills</h2><p>Skills and tools...</p></section>
      <section id="certification"><h2>Certification</h2><p>Certifications and achievements...</p>
            <h2>Certification</h2>
            <p>Below are some of my achievements and certifications:</p>
            <div class="certificates-container">
              <div class="certificate-card"><img src="" alt="Certificate 1" /></div>
              <div class="certificate-card"><img src="certificates/cert2.jpg" alt="Certificate 2" /></div>
              <div class="certificate-card"><img src="certificates/cert3.jpg" alt="Certificate 3" /></div>
              <div class="certificate-card"><img src="certificates/cert4.jpg" alt="Certificate 4" /></div>
              <div class="certificate-card"><img src="certificates/cert5.jpg" alt="Certificate 5" /></div>
              <div class="certificate-card"><img src="certificates/cert6.jpg" alt="Certificate 6" /></div>
            </div>
          </section>
      <section id="education"><h2>Education</h2><p>Academic background...</p></section>
      <section id="contact"><h2>Contact</h2><p>Contact form or info...</p></section>
      <section id="resume"><h2>Resume</h2><p>Resume download or preview...</p></section>

      <footer>
        <p>&copy; 2025 Abhishek Kandel. All rights reserved.</p>
      </footer>
    </main>
  </div>
  <script>
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({ behavior: 'smooth' });
        }
      });
    });

    console.log("Website loaded successfully!");
  </script>
</body>
</html>
