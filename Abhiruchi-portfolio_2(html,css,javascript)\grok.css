* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: Arial, sans-serif;
}

body {
    background-color: #f4f4f4;
    color: #fff;
    line-height: 1.6;
    overflow-x: hidden;
    width: 100%;
    max-width: 100%;
}

.container {
    display: flex;
    height: 100vh;
    width: 100%;
    max-width: 100%;
    position: relative;
    overflow-x: hidden;
}

.sidebar {
    width: 250px;
    background-color: #fff;
    padding: 20px;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    top: 0;
    left: 0;
}

.sidebar nav ul {
    list-style: none;
}

.sidebar nav ul li {
    margin-bottom: 15px;
}

.sidebar nav ul li a {
    color: #2e7d32;
    text-decoration: none;
    font-size: 16px;
    display: flex;
    align-items: center;
    padding: 10px 15px;
    border-radius: 5px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    font-weight: 500;
}

.sidebar nav ul li a i {
    color: #2e7d32;
    font-size: 18px;
    width: 24px;
    text-align: center;
}

.sidebar nav ul li a:hover {
    background-color: #e8f5e9;
}

.sidebar nav ul li a.active {
    background-color: #4CAF50;
    color: white;
    font-weight: 600;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.sidebar nav ul li a.active i {
    color: white;
}

.sidebar nav ul li a.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 4px;
    background-color: #2E7D32;
}

.sidebar nav ul li a i {
    margin-right: 10px;
}

.content {
    margin-left: 250px;
    flex: 1;
    background-color: #4caf50;
    padding: 20px;
    position: relative;
    width: calc(100% - 250px);
    overflow-x: hidden;
}

header {
    text-align: right;
    margin-bottom: 20px;
}

.email-login {
    background-color: #1976d2;
    color: #fff;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 5px;
}

.hero {
    text-align: center;
    padding: 50px 0;
}

.hero h1 {
    font-size: 48px;
    color: #fff;
    margin-bottom: 10px;
}

.hero h2 {
    font-size: 24px;
    color: #ffca28;
    margin-bottom: 20px;
}

.hero p {
    font-size: 18px;
    max-width: 600px;
    margin: 0 auto 30px;
}

.social-icons {
    margin-bottom: 30px;
}

.social-icons a {
    color: #fff;
    font-size: 24px;
    margin: 0 10px;
    text-decoration: none;
}

.buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.btn {
    background-color: #2e7d32;
    color: #fff;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #1b5e20;
}

.main-footer {
    background-color: #333;
    color: #fff;
    text-align: center;
    padding: 30px 0;
    margin-top: 50px;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.copyright {
    font-size: 14px;
    margin-bottom: 20px;
}

.copyright a {
    color: #4CAF50;
    text-decoration: none;
}

.copyright a:hover {
    text-decoration: underline;
}

.footer-social {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.footer-social a {
    color: #fff;
    font-size: 18px;
    transition: color 0.3s ease;
}

.footer-social a:hover {
    color: #4CAF50;
}

/* About Section Styles */
.about-section {
    padding: 40px 20px;
    background-color: #fff;
    color: #333;
    margin: 30px 0;
}

.about-section h2 {
    font-size: 32px;
    color: #333;
    margin-bottom: 25px;
    text-align: center;
}

.about-content {
    max-width: 900px;
    margin: 0 auto;
    line-height: 1.8;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.profile-image-container {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border: 5px solid #fff;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.profile-image-container:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.profile-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.profile-image:hover {
    transform: scale(1.1);
}

.about-content p {
    margin-bottom: 20px;
    text-align: justify;
}

.about-content a {
    color: #2e7d32;
    text-decoration: none;
    font-weight: 600;
}

.about-content a:hover {
    text-decoration: underline;
}

.skills-overview {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin: 30px 0;
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 8px;
}

.skill-category {
    flex: 1 1 200px;
}

.skill-category h4 {
    color: #2e7d32;
    margin-bottom: 10px;
    font-weight: 600;
}

.current-focus {
    background-color: #e8f5e9;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #2e7d32;
}

.current-focus h4 {
    color: #2e7d32;
    margin-bottom: 10px;
}

/* Experience Section Styles */
.experience-section {
    padding: 40px 20px;
    background-color: #f5f5f5;
    margin: 30px 0;
}

.experience-section h2 {
    font-size: 32px;
    color: #333;
    margin-bottom: 30px;
    text-align: center;
}

.experience-container {
    max-width: 900px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.experience-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.experience-header {
    display: flex;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.company-logo {
    width: 80px;
    height: 80px;
    margin-right: 20px;
    flex-shrink: 0;
}

.company-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 5px;
}

.company-info {
    flex: 1;
}

.company-name {
    font-size: 20px;
    color: #2e7d32;
    margin-bottom: 5px;
}

.job-title {
    font-size: 16px;
    color: #555;
    margin-bottom: 5px;
    font-weight: 600;
}

.job-duration {
    font-size: 14px;
    color: #777;
}

.job-description {
    padding: 20px;
}

.job-description ul {
    list-style-type: disc;
    padding-left: 20px;
    margin-bottom: 15px;
}

.job-description li {
    margin-bottom: 10px;
    color: #555;
}

.tools-used {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px dashed #ddd;
}

.tools-label {
    font-weight: 600;
    color: #333;
    margin-right: 10px;
}

.tools-list {
    color: #555;
}

/* Projects Section Styles */
.projects-section {
    padding: 40px 20px;
    background-color: #fff;
    margin: 30px 0;
}

.projects-section h2 {
    font-size: 32px;
    color: #333;
    margin-bottom: 30px;
    text-align: center;
}

.projects-container {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
}

.project-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.project-image {
    height: 200px;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.project-card:hover .project-image img {
    transform: scale(1.05);
}

.project-content {
    padding: 20px;
}

.project-title {
    font-size: 20px;
    color: #2e7d32;
    margin-bottom: 10px;
}

.project-description {
    color: #555;
    margin-bottom: 10px;
}

.project-status {
    display: inline-block;
    padding: 3px 10px;
    background-color: #e8f5e9;
    color: #2e7d32;
    border-radius: 15px;
    font-size: 14px;
    margin-bottom: 15px;
}

.project-tech ul {
    list-style-type: none;
    padding: 0;
    margin-bottom: 15px;
}

.project-tech li {
    margin-bottom: 5px;
    color: #555;
    font-size: 14px;
}

.project-links {
    display: flex;
    gap: 15px;
}

.project-links a {
    display: inline-block;
    padding: 8px 15px;
    background-color: #2e7d32;
    color: #fff;
    text-decoration: none;
    border-radius: 5px;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.project-links a:hover {
    background-color: #1b5e20;
}

/* Skills Section Styles */
.skills-section {
    padding: 40px 20px;
    background-color: #f5f5f5;
    margin: 30px 0;
}

.skills-section h2 {
    font-size: 32px;
    color: #333;
    margin-bottom: 30px;
    text-align: center;
}

.skills-container {
    max-width: 1000px;
    margin: 0 auto;
}

.skill-category-section {
    margin-bottom: 40px;
}

.skill-category-section h3 {
    font-size: 24px;
    color: #2e7d32;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e0e0e0;
}

.skill-icons {
    display: flex;
    flex-wrap: nowrap;
    gap: 15px;
    justify-content: center;
    overflow-x: auto;
    padding: 15px 5px;
}

.skill-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 100px;
    width: 100px;
    margin: 5px;
    padding: 10px;
    border-radius: 10px;
    transition: all 0.3s ease;
    background-color: rgba(255, 255, 255, 0.7);
    flex-shrink: 0;
}

.skill-icon:hover {
    transform: translateY(-5px);
}

.skill-icon img {
    width: 60px;
    height: 60px;
    object-fit: contain;
    display: block;
    margin: 0 auto 8px auto;
}

.skill-icon:hover img {
    transform: scale(1.1);
}

.skill-icon span {
    font-size: 12px;
    font-weight: 600;
    color: #444;
    text-align: center;
}

/* Contact Section Styles */
.contact-section {
    padding: 40px 20px;
    background-color: #fff;
    margin: 30px 0;
}

/* Resume Section Styles */
.resume-section {
    padding: 60px 20px;
    background-color: #2e7d32;
    margin: 30px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.resume-section:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><path d="M96,95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-10,0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9zm10,0h9v-9h-9v9z" fill="rgba(255,255,255,0.05)"/></svg>');
    opacity: 0.3;
}

.resume-section h2 {
    font-size: 36px;
    color: #fff;
    margin-bottom: 30px;
    position: relative;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.resume-section h2:after {
    content: '';
    display: block;
    width: 80px;
    height: 3px;
    background-color: #fff;
    margin: 15px auto 0;
    border-radius: 2px;
}

.resume-container {
    max-width: 700px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.resume-content {
    background-color: #fff;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transform: translateY(0);
    transition: transform 0.3s ease;
}

.resume-content:hover {
    transform: translateY(-5px);
}

.resume-content p {
    margin-bottom: 30px;
    color: #555;
    font-size: 18px;
    line-height: 1.6;
}

.download-btn {
    display: inline-block;
    padding: 15px 30px;
    background-color: #2e7d32;
    color: #fff;
    text-decoration: none;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    z-index: 1;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.download-btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.6s ease;
    z-index: -1;
}

.download-btn:hover {
    background-color: #1b5e20;
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.download-btn:hover:before {
    left: 100%;
}

.download-btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.download-btn i {
    margin-right: 10px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-5px);
    }
    60% {
        transform: translateY(-3px);
    }
}

/* Already defined above */

.contact-section h2 {
    font-size: 32px;
    color: #333;
    margin-bottom: 30px;
    text-align: center;
}

.contact-container {
    max-width: 600px;
    margin: 0 auto;
}

.contact-form {
    background-color: #f9f9f9;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15),
                0 5px 15px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.6);
    transform: perspective(1000px) rotateX(2deg);
    transform-style: preserve-3d;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.8);
    background-image: linear-gradient(to bottom, #ffffff, #f5f5f5);
}

.contact-form h3 {
    font-size: 28px;
    color: #2e7d32;
    margin-bottom: 25px;
    text-align: center;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.9);
    transform: translateZ(30px);
    letter-spacing: 1px;
    position: relative;
}

.contact-form h3:after {
    content: '';
    display: block;
    width: 60px;
    height: 3px;
    background-color: #2e7d32;
    margin: 15px auto 0;
    border-radius: 2px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transform: translateZ(5px);
}

.form-group {
    margin-bottom: 25px;
    position: relative;
    transform-style: preserve-3d;
}

.form-group::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    transform: translateZ(5px);
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.form-group:hover::before {
    opacity: 1;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    transform: translateZ(10px);
    background-color: #fff;
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: #2e7d32;
    outline: none;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05),
                0 0 8px rgba(46, 125, 50, 0.4);
    transform: translateZ(15px);
}

.form-group textarea {
    min-height: 150px;
    resize: vertical;
}

.submit-btn {
    display: block;
    width: 100%;
    padding: 15px;
    background-color: #2e7d32;
    color: #fff;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2),
                0 2px 4px rgba(0, 0, 0, 0.1);
    transform: translateZ(20px);
    position: relative;
    overflow: hidden;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
    letter-spacing: 0.5px;
}

.submit-btn:hover {
    background-color: #1b5e20;
    transform: translateZ(25px) scale(1.02);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.25),
                0 3px 6px rgba(0, 0, 0, 0.15);
}

.submit-btn:active {
    transform: translateZ(15px) scale(0.98);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    background-color: #164a18;
}

/* Education Section Styles */
.education-section {
    padding: 40px 20px;
    background-color: #008080;
    color: #fff;
    margin: 30px 0;
}

.education-section h2 {
    font-size: 36px;
    text-align: center;
    margin-bottom: 30px;
    letter-spacing: 2px;
    font-weight: 600;
}

.education-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    justify-content: center;
    max-width: 1200px;
    margin: 0 auto;
}

.education-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 25px;
    width: 48%;
    min-width: 400px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.university-name {
    color: #008080;
    font-size: 22px;
    margin-bottom: 10px;
    font-weight: 600;
}

.university-location {
    color: #666;
    font-size: 16px;
    margin-bottom: 20px;
}

.education-details {
    margin-bottom: 20px;
}

.degree-info, .cgpa-info {
    margin-bottom: 10px;
}

.label {
    color: #333;
    font-weight: 600;
    margin-right: 5px;
}

.value {
    color: #555;
}

.coursework h4 {
    color: #333;
    margin-bottom: 10px;
    font-weight: 600;
}

.coursework ul {
    list-style-type: disc;
    padding-left: 20px;
}

.coursework li {
    color: #555;
    margin-bottom: 5px;
}

.previous-school {
    color: #555;
    line-height: 1.6;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 5px;
    border-left: 3px solid #008080;
}

.previous-school strong {
    color: #008080;
    font-size: 16px;
}

.school-detail {
    margin-bottom: 8px;
}

.school-detail .label {
    display: inline-block;
    width: 100px;
    color: #666;
    font-weight: 600;
}

.school-detail .value {
    color: #333;
    font-weight: 500;
}

/* Certificate Section Styles */
.certification-section {
    padding: 40px 20px;
    background-color: #3b8d40;
    border-radius: 10px;
    margin: 30px 0;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.certification-section h2 {
    color: #fff;
    font-size: 32px;
    margin-bottom: 15px;
    text-align: center;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.certification-section p {
    color: #fff;
    text-align: center;
    margin-bottom: 30px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.certificates-container {
    display: flex;
    flex-wrap: wrap;
    gap: 25px;
    justify-content: center;
    max-width: 1200px;
    margin: 0 auto;
}

.certificate-frame {
    width: 280px;
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    position: relative;
}

.certificate-frame:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.certificate-upload {
    height: 200px;
    background-color: #f5f5f5;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
    border: 2px dashed #ccc;
    margin: 15px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.certificate-upload:hover {
    border-color: #2e7d32;
    background-color: #f0f7f0;
}

.certificate-input {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    z-index: 2;
}

.upload-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #666;
    z-index: 1;
    transition: transform 0.3s ease;
}

.certificate-upload:hover .upload-label {
    transform: scale(1.05);
}

.upload-label i {
    font-size: 36px;
    margin-bottom: 10px;
    color: #2e7d32;
}

.preview-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    background-color: #fff;
}

.preview-container img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.certificate-title {
    padding: 15px;
    text-align: center;
    color: #333;
    font-weight: bold;
    border-top: 1px solid #eee;
    transition: background-color 0.3s ease;
}

.certificate-title:focus {
    outline: none;
    background-color: #f9f9f9;
}

/* Certificate controls removed */

/* Animation for certificate frames */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.certificate-frame {
    animation: fadeIn 0.5s ease forwards;
}

.certificate-frame:nth-child(1) { animation-delay: 0.1s; }
.certificate-frame:nth-child(2) { animation-delay: 0.2s; }
.certificate-frame:nth-child(3) { animation-delay: 0.3s; }
.certificate-frame:nth-child(4) { animation-delay: 0.4s; }
.certificate-frame:nth-child(5) { animation-delay: 0.5s; }
.certificate-frame:nth-child(6) { animation-delay: 0.6s; }

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
    }
    .sidebar {
        width: 100%;
        position: static;
        height: auto;
    }
    .content {
        margin-left: 0;
    }
    .hero {
        padding: 20px 0;
    }
    .social-icons a {
        font-size: 18px;
    }
    .certificates-container {
        flex-direction: column;
        align-items: center;
    }
    .certificate-frame {
        width: 90%;
    }

    .education-container {
        flex-wrap: wrap;
    }

    .education-card {
        max-width: 100%;
        padding: 15px;
    }

    .university-name {
        font-size: 18px;
    }

    .university-location {
        font-size: 14px;
    }
}