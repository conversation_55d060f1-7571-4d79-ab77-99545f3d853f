<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200">
  <title>matplotlib Logo</title>
  <g transform="translate(40,20)">
    <rect x="0" y="0" width="120" height="120" rx="10" ry="10" fill="#11557C"/>
    <g transform="translate(10,10)">
      <path d="M0,100 L20,80 L40,85 L60,40 L80,60 L100,20" stroke="#FFC107" stroke-width="4" fill="none"/>
      <circle cx="0" cy="100" r="5" fill="#4CAF50"/>
      <circle cx="20" cy="80" r="5" fill="#4CAF50"/>
      <circle cx="40" cy="85" r="5" fill="#4CAF50"/>
      <circle cx="60" cy="40" r="5" fill="#4CAF50"/>
      <circle cx="80" cy="60" r="5" fill="#4CAF50"/>
      <circle cx="100" cy="20" r="5" fill="#4CAF50"/>
    </g>
  </g>
  <text x="100" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" fill="#11557C">matplotlib</text>
</svg>
